<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统架构 - 12345智慧政务平台</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/architecture.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <h1 class="logo">
                <i class="fas fa-phone-alt"></i>
                12345智慧政务平台
            </h1>
            <nav class="nav">
                <a href="index.html" class="nav-link">总览</a>
                <a href="roles.html" class="nav-link">角色体系</a>
                <a href="workflow.html" class="nav-link">业务流程</a>
                <a href="operations.html" class="nav-link">功能操作</a>
                <a href="ai-enhancement.html" class="nav-link">智能赋能</a>
                <a href="architecture.html" class="nav-link active">系统架构</a>
                <a href="system-features.html" class="nav-link">效果展示</a>
            </nav>
        </div>
    </header>

    <main class="main">
        <section class="page-header">
            <div class="container">
                <h1 class="page-title">系统架构</h1>
                <p class="page-subtitle">技术架构与组织架构双重保障，确保系统稳定高效运行</p>
            </div>
        </section>

        <section class="architecture-overview">
            <div class="container">
                <div class="architecture-stats">
                    <div class="stat-card">
                        <div class="stat-icon tech">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">微服务</div>
                            <div class="stat-label">技术架构</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon org">
                            <i class="fas fa-sitemap"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">5级</div>
                            <div class="stat-label">组织层级</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon ai">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">AI驱动</div>
                            <div class="stat-label">智能引擎</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon security">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="stat-info">
                            <div class="stat-number">多重</div>
                            <div class="stat-label">安全保障</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="architecture-sections">
            <div class="container">
                <!-- 技术架构 -->
                <div class="architecture-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="section-info">
                            <h2 class="section-title">技术架构</h2>
                            <p class="section-subtitle">现代化微服务架构，支撑高并发、高可用的政务服务</p>
                        </div>
                    </div>
                    
                    <div class="section-content">
                        <div class="tech-layers">
                            <!-- 表现层 -->
                            <div class="tech-layer presentation">
                                <div class="layer-header">
                                    <h3><i class="fas fa-desktop"></i> 表现层</h3>
                                </div>
                                <div class="layer-components">
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-mobile-alt"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>移动端应用</h4>
                                            <p>iOS/Android原生应用，支持现场处理、语音录入、图片上传等功能</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-globe"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>Web管理平台</h4>
                                            <p>基于Vue.js的响应式Web应用，支持工单管理、数据分析、系统配置</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-headset"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>呼叫中心界面</h4>
                                            <p>专业的呼叫中心工作台，集成CTI、CRM、知识库等功能</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 应用层 -->
                            <div class="tech-layer application">
                                <div class="layer-header">
                                    <h3><i class="fas fa-cogs"></i> 应用层</h3>
                                </div>
                                <div class="layer-components">
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>工单管理服务</h4>
                                            <p>工单创建、流转、审核、关闭的完整生命周期管理</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>用户权限服务</h4>
                                            <p>基于角色的权限控制，支持多级组织架构和细粒度权限管理</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-bell"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>消息通知服务</h4>
                                            <p>支持短信、邮件、APP推送等多种通知方式</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-chart-bar"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>数据分析服务</h4>
                                            <p>实时统计分析、报表生成、绩效评估等功能</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- AI智能层 -->
                            <div class="tech-layer ai-layer">
                                <div class="layer-header">
                                    <h3><i class="fas fa-brain"></i> AI智能层</h3>
                                </div>
                                <div class="layer-components">
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-microphone"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>语音识别引擎</h4>
                                            <p>实时语音转文字，支持方言识别和噪音过滤</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-language"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>自然语言处理</h4>
                                            <p>文本理解、情感分析、意图识别、实体抽取</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-search"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>智能知识库</h4>
                                            <p>基于向量数据库的语义搜索和智能问答</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-route"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>智能分派引擎</h4>
                                            <p>基于规则和机器学习的自动分派决策</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 数据层 -->
                            <div class="tech-layer data">
                                <div class="layer-header">
                                    <h3><i class="fas fa-database"></i> 数据层</h3>
                                </div>
                                <div class="layer-components">
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-database"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>关系型数据库</h4>
                                            <p>MySQL集群，存储工单、用户、组织等结构化数据</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-memory"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>缓存数据库</h4>
                                            <p>Redis集群，提供高性能缓存和会话存储</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-file-archive"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>文件存储</h4>
                                            <p>分布式文件系统，存储图片、音频、文档等非结构化数据</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-search-plus"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>搜索引擎</h4>
                                            <p>Elasticsearch集群，提供全文搜索和日志分析</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 基础设施层 -->
                            <div class="tech-layer infrastructure">
                                <div class="layer-header">
                                    <h3><i class="fas fa-cloud"></i> 基础设施层</h3>
                                </div>
                                <div class="layer-components">
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-server"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>容器化部署</h4>
                                            <p>基于Docker和Kubernetes的容器化部署和编排</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-network-wired"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>服务网格</h4>
                                            <p>Istio服务网格，提供服务间通信、安全和监控</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-eye"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>监控告警</h4>
                                            <p>Prometheus + Grafana监控体系，实时监控系统健康状态</p>
                                        </div>
                                    </div>
                                    <div class="component">
                                        <div class="component-icon">
                                            <i class="fas fa-shield-alt"></i>
                                        </div>
                                        <div class="component-info">
                                            <h4>安全防护</h4>
                                            <p>WAF、DDoS防护、数据加密、访问控制等多重安全保障</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 组织架构 -->
                <div class="architecture-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-sitemap"></i>
                        </div>
                        <div class="section-info">
                            <h2 class="section-title">组织架构</h2>
                            <p class="section-subtitle">五级组织体系，条块结合的管理模式</p>
                        </div>
                    </div>
                    
                    <div class="section-content">
                        <div class="org-structure">
                            <div class="org-level level-1">
                                <div class="level-header">
                                    <h3>市级层面</h3>
                                    <span class="level-badge">一级</span>
                                </div>
                                <div class="org-units">
                                    <div class="org-unit primary">
                                        <div class="unit-icon">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="unit-info">
                                            <h4>市级12345中心</h4>
                                            <p>统一受理平台，负责工单创建、一级分派和回访关闭</p>
                                        </div>
                                    </div>
                                    <div class="org-unit">
                                        <div class="unit-icon">
                                            <i class="fas fa-university"></i>
                                        </div>
                                        <div class="unit-info">
                                            <h4>市级职能部门</h4>
                                            <p>市住建局、市环保局、市市场监管局等专业管理部门</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="org-level level-2">
                                <div class="level-header">
                                    <h3>区/县级层面</h3>
                                    <span class="level-badge">二级</span>
                                </div>
                                <div class="org-units">
                                    <div class="org-unit primary">
                                        <div class="unit-icon">
                                            <i class="fas fa-building-flag"></i>
                                        </div>
                                        <div class="unit-info">
                                            <h4>区/县12345分中心</h4>
                                            <p>区级总承办单位，负责区内二次分派和统筹协调</p>
                                        </div>
                                    </div>
                                    <div class="org-unit">
                                        <div class="unit-icon">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="unit-info">
                                            <h4>区级职能部门</h4>
                                            <p>区住建局、区城管局、区市场监管局等专业执行部门</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="org-level level-3">
                                <div class="level-header">
                                    <h3>街道/镇级层面</h3>
                                    <span class="level-badge">三级</span>
                                </div>
                                <div class="org-units">
                                    <div class="org-unit primary">
                                        <div class="unit-icon">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </div>
                                        <div class="unit-info">
                                            <h4>街道办事处/镇政府</h4>
                                            <p>属地管理主体，负责辖区内工单的统筹处理</p>
                                        </div>
                                    </div>
                                    <div class="org-unit">
                                        <div class="unit-icon">
                                            <i class="fas fa-briefcase"></i>
                                        </div>
                                        <div class="unit-info">
                                            <h4>街道内设部门</h4>
                                            <p>街道城管科、综治办、社会事务科等专业科室</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="org-level level-4">
                                <div class="level-header">
                                    <h3>社区/村级层面</h3>
                                    <span class="level-badge">四级</span>
                                </div>
                                <div class="org-units">
                                    <div class="org-unit primary">
                                        <div class="unit-icon">
                                            <i class="fas fa-home"></i>
                                        </div>
                                        <div class="unit-info">
                                            <h4>社区居委会/村委会</h4>
                                            <p>最基层的管理单位，直接面向居民提供服务</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="org-level level-5">
                                <div class="level-header">
                                    <h3>网格/执行层面</h3>
                                    <span class="level-badge">五级</span>
                                </div>
                                <div class="org-units">
                                    <div class="org-unit primary">
                                        <div class="unit-icon">
                                            <i class="fas fa-user-tie"></i>
                                        </div>
                                        <div class="unit-info">
                                            <h4>网格员/工作人员</h4>
                                            <p>最终执行者，负责现场处理和问题解决</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统特性 -->
                <div class="architecture-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="section-info">
                            <h2 class="section-title">系统特性</h2>
                            <p class="section-subtitle">高可用、高性能、高安全的系统保障</p>
                        </div>
                    </div>
                    
                    <div class="section-content">
                        <div class="features-grid">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <h3>高性能</h3>
                                <ul>
                                    <li>支持万级并发用户</li>
                                    <li>毫秒级响应时间</li>
                                    <li>弹性伸缩能力</li>
                                    <li>智能负载均衡</li>
                                </ul>
                            </div>
                            
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <h3>高安全</h3>
                                <ul>
                                    <li>数据加密传输存储</li>
                                    <li>多因子身份认证</li>
                                    <li>细粒度权限控制</li>
                                    <li>安全审计日志</li>
                                </ul>
                            </div>
                            
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-heartbeat"></i>
                                </div>
                                <h3>高可用</h3>
                                <ul>
                                    <li>99.9%系统可用性</li>
                                    <li>多地域容灾备份</li>
                                    <li>自动故障恢复</li>
                                    <li>实时健康监控</li>
                                </ul>
                            </div>
                            
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-expand-arrows-alt"></i>
                                </div>
                                <h3>可扩展</h3>
                                <ul>
                                    <li>微服务架构设计</li>
                                    <li>插件化功能扩展</li>
                                    <li>API开放接口</li>
                                    <li>多租户支持</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 12345智慧政务平台 智能化政务服务解决方案.</p>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/architecture.js"></script>
</body>
</html>
