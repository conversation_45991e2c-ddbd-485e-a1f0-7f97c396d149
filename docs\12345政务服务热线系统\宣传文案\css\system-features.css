/* 系统功能页面专用样式 */

.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 300;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.page-title i {
    font-size: 2rem;
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* 功能区域 */
.features-section {
    padding: 4rem 0;
    background: #f8f9fa;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* 功能卡片 */
.feature-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.feature-card.available {
    border-left: 4px solid #26de81;
}

.feature-card.coming-soon {
    border-left: 4px solid #ffa726;
    opacity: 0.8;
}

.feature-card.coming-soon:hover {
    opacity: 1;
}

/* 功能图标 */
.feature-icon {
    padding: 3rem 2rem;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
}

.feature-icon i {
    font-size: 3rem;
    color: #667eea;
    transition: all 0.3s ease;
}

.feature-card.available .feature-icon i {
    color: #26de81;
}

.feature-card.coming-soon .feature-icon i {
    color: #ffa726;
}

.feature-card:hover .feature-icon i {
    transform: scale(1.1);
}

/* 功能内容 */
.feature-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.feature-title {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
}

.feature-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

/* 功能标签 */
.feature-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.tag {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.feature-card.available .tag {
    background: #e8f5e8;
    color: #2e7d32;
}

.feature-card.coming-soon .tag {
    background: #fff3e0;
    color: #f57c00;
}

/* 功能操作 */
.feature-actions {
    padding: 0 1.5rem 1.5rem;
}

.btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #26de81 0%, #20bf6b 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 100%;
    justify-content: center;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(38, 222, 129, 0.3);
    color: white !important;
    text-decoration: none;
    background: linear-gradient(135deg, #20bf6b 0%, #1da1f2 100%) !important;
}

.btn-disabled {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #e9ecef;
    color: #6c757d;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: not-allowed;
    width: 100%;
    justify-content: center;
}

/* 功能状态 */
.feature-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.feature-status.available {
    background: #e8f5e8;
    color: #2e7d32;
}

.feature-status.coming-soon {
    background: #fff3e0;
    color: #f57c00;
}

.feature-status i {
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .feature-card {
        min-height: 350px;
    }
    
    .feature-icon {
        padding: 2rem 1.5rem;
        min-height: 100px;
    }
    
    .feature-icon i {
        font-size: 2.5rem;
    }
    
    .feature-content {
        padding: 1rem;
    }
    
    .feature-actions {
        padding: 0 1rem 1rem;
    }
}

@media (max-width: 480px) {
    .features-section {
        padding: 2rem 0;
    }
    
    .page-title {
        font-size: 1.8rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
}